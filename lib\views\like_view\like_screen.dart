import 'package:room_eight/core/api_config/endpoints/api_endpoint.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/viewmodels/like_bloc/like_bloc.dart';
import 'package:room_eight/views/like_view/widgets/like_screen_shimmer.dart';
import 'package:room_eight/widgets/common_widget/app_alert_dialog.dart';
import 'package:room_eight/views/chat/model/search_user_model.dart';

class LikeScreen extends StatefulWidget {
  const LikeScreen({super.key});

  static Widget builder(BuildContext context) => const LikeScreen();

  @override
  State<LikeScreen> createState() => _LikeScreenState();
}

class _LikeScreenState extends State<LikeScreen> {
  static const int _moveOutTabIndex = 0;
  static const int _moveInTabIndex = 1;

  @override
  void initState() {
    super.initState();
    _loadInitialData();
  }

  void _loadInitialData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final bloc = context.read<LikeBloc>();
      bloc.add(LoadMoveOutData());
      bloc.add(LoadMoveInData());
    });
  }

  void _navigateToChat(dynamic user) {
    final userId = _extractUserId(user);
    final chatUser = _createChatUser(user, userId);

    NavigatorService.pushNamed(
      AppRoutes.chatscreen,
      arguments: [chatUser, () {}],
    );
  }

  int? _extractUserId(dynamic user) {
    final userType = user.runtimeType.toString();

    if (userType.contains('MoveOutUser')) {
      return user.messageId ?? user.id;
    } else if (userType.contains('MoveInUser')) {
      return user.toMessageId ?? user.id;
    }
    return user.id;
  }

  SearchUserData _createChatUser(dynamic user, int? userId) {
    return SearchUserData(
      userId: userId,
      name: user.name,
      userName: user.name,
      profileImage: user.profileImage,
    );
  }

  void _onTabChanged(int index) {
    final bloc = context.read<LikeBloc>();
    bloc.add(TabChangedEvent(index));
    _loadDataForTab(index, bloc);
  }

  void _loadDataForTab(int index, LikeBloc bloc) {
    final state = bloc.state;

    if (index == _moveOutTabIndex &&
        state.moveOutData.isEmpty &&
        !state.isLoadingMoveOut) {
      bloc.add(LoadMoveOutData());
    } else if (index == _moveInTabIndex &&
        state.moveInData.isEmpty &&
        !state.isLoadingMoveIn) {
      bloc.add(LoadMoveInData());
    }
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        backgroundColor: Theme.of(context).customColors.scaffoldColor,
        body: BlocConsumer<LikeBloc, LikeState>(
          listener: (context, state) {
            // Handle state changes if needed
          },
          builder: (context, state) {
            if (state.isLoadData || state.isAcceptingLike) {
              return const LikeScreenShimmer();
            }

            return _buildMainContent();
          },
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.r),
      child: Column(
        children: [
          buildSizedBoxH(50.h),
          _buildTabView(),
          Expanded(child: _buildPageView()),
        ],
      ),
    );
  }

  Widget _buildTabView() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 5.h),
      decoration: BoxDecoration(
        color: Theme.of(context).customColors.fillColor,
        borderRadius: BorderRadius.circular(100.r),
      ),
      child: TabBar(
        onTap: _onTabChanged,
        indicatorSize: TabBarIndicatorSize.tab,
        labelStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
          fontSize: 16.sp,
          fontWeight: FontWeight.w500,
        ),
        labelColor: Theme.of(context).customColors.fillColor,
        dividerColor: Colors.transparent,
        overlayColor: WidgetStateProperty.all(Colors.transparent),
        labelPadding: EdgeInsets.zero,
        indicator: ShapeDecoration(
          color: Theme.of(context).customColors.primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(100.r),
          ),
        ),
        tabs: [
          Tab(text: Lang.of(context).lbl_move_out),
          Tab(text: Lang.of(context).lbl_move_in),
        ],
      ),
    );
  }

  Widget _buildPageView() {
    return PageView(
      controller: context.read<LikeBloc>().pageController,
      physics: const NeverScrollableScrollPhysics(),
      children: [
        _LikeTabView(
          isLoading: (state) => state.isLoadingMoveOut,
          data: (state) => state.moveOutData,
          onRefresh: () => context.read<LikeBloc>().add(LoadMoveOutData()),
          emptyMessage: 'No Move Out data available',
          emptyIcon: Icons.favorite_border,
          onUserTap: _navigateToChat,
          onLike: null, 
          onDislike: null,
        ),
        _LikeTabView(
          isLoading: (state) => state.isLoadingMoveIn,
          data: (state) => state.moveInData,
          onRefresh: () => context.read<LikeBloc>().add(LoadMoveInData()),
          emptyMessage: 'No Move In data available',
          emptyIcon: Icons.favorite,
          onUserTap: _navigateToChat,
          onLike: _handleAcceptLike,
          onDislike: _handleRejectLike,
        ),
      ],
    );
  }

  void _handleAcceptLike(dynamic user) {
    _showAcceptLikeDialog(user);
  }

  void _handleRejectLike(dynamic user) {
    if (user.id != null) {
      context.read<LikeBloc>().add(
        AcceptLikeEvent(profileId: user.id!, isAccept: false),
      );
    }
  }

  void _showAcceptLikeDialog(dynamic user) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return CustomAlertDialog(
          title: 'Accept Like',
          subtitle:
              'Are you sure you want to accept this like from ${user.name ?? 'this user'}?',
          confirmButtonText: 'Accept',
          cancelButtonText: 'Cancel',
          isLoading: false,
          onConfirmButtonPressed: () {
            Navigator.pop(dialogContext);
            if (user.id != null) {
              context.read<LikeBloc>().add(
                AcceptLikeEvent(
                  profileId: user.id!,
                  isAccept: true,
                  user: user,
                ),
              );
            }
          },
          onCancelButtonPressed: () {
            Navigator.pop(dialogContext);
          },
        );
      },
    );
  }
}

class _LikeTabView extends StatelessWidget {
  final bool Function(LikeState) isLoading;
  final List<dynamic> Function(LikeState) data;
  final VoidCallback onRefresh;
  final String emptyMessage;
  final IconData emptyIcon;
  final Function(dynamic) onUserTap;
  final Function(dynamic)? onLike;
  final Function(dynamic)? onDislike;

  const _LikeTabView({
    required this.isLoading,
    required this.data,
    required this.onRefresh,
    required this.emptyMessage,
    required this.emptyIcon,
    required this.onUserTap,
    this.onLike,
    this.onDislike,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LikeBloc, LikeState>(
      builder: (context, state) {
        if (isLoading(state)) {
          return const LikeScreenShimmer();
        }

        final users = data(state);
        if (users.isEmpty) {
          return _buildEmptyState(context);
        }

        return _buildUserList(context, users);
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(emptyIcon, size: 64.r, color: Colors.grey),
          buildSizedBoxH(16.h),
          Text(
            emptyMessage,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.grey,
              fontSize: 16.sp,
            ),
          ),
          buildSizedBoxH(20.h),
          ElevatedButton(onPressed: onRefresh, child: const Text('Refresh')),
        ],
      ),
    );
  }

  Widget _buildUserList(BuildContext context, List<dynamic> users) {
    return RefreshIndicator(
      onRefresh: () async => onRefresh(),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Padding(
          padding: EdgeInsets.only(bottom: 80.w),
          child: Column(
            children: [
              buildSizedBoxH(20.h),
              ...users.map((user) => _buildUserItem(context, user)),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUserItem(BuildContext context, dynamic user) {
    return Column(
      children: [
        _UserProfileCard(
          user: user,
          onTap: () => onUserTap(user),
          onLike: onLike != null ? () => onLike!(user) : null,
          onDislike: onDislike != null ? () => onDislike!(user) : null,
          onFavorite: () {}, // Placeholder for favorite functionality
        ),
        buildSizedBoxH(20.h),
      ],
    );
  }
}

class _UserProfileCard extends StatelessWidget {
  final dynamic user;
  final VoidCallback onTap;
  final VoidCallback? onLike;
  final VoidCallback? onDislike;
  final VoidCallback onFavorite;

  const _UserProfileCard({
    required this.user,
    required this.onTap,
    this.onLike,
    this.onDislike,
    required this.onFavorite,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(8.r),
        decoration: _buildCardDecoration(context),
        child: Column(
          children: [
            _buildUserImage(context),
            buildSizedBoxH(12.h),
            _buildUserInfo(context),
            _buildUserDescription(context),
          ],
        ),
      ),
    );
  }

  BoxDecoration _buildCardDecoration(BuildContext context) {
    return BoxDecoration(
      color: Theme.of(context).customColors.fillColor,
      borderRadius: BorderRadius.circular(16.r),
      boxShadow: const [
        BoxShadow(color: Colors.black12, blurRadius: 8, offset: Offset(0, 4)),
      ],
    );
  }

  Widget _buildUserImage(BuildContext context) {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(12.r),
          child: CustomImageView(
            imagePath: ApiEndPoint.getImageUrl + (user.profileImage ?? ''),
            height: 180.h,
            width: double.infinity,
            fit: BoxFit.cover,
          ),
        ),
        Positioned(top: 10.h, right: 10.w, child: _buildFavoriteButton()),
      ],
    );
  }

  Widget _buildFavoriteButton() {
    return InkWell(
      onTap: onFavorite,
      child: CircleAvatar(
        radius: 20.r,
        backgroundColor: Colors.white70,
        child: CustomImageView(
          imagePath: Assets.images.svgs.icons.icLike.path,
          margin: EdgeInsets.all(10.r),
        ),
      ),
    );
  }

  Widget _buildUserInfo(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          user.name?.isNotEmpty == true ? user.name! : 'Unknown User',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16.sp),
        ),
        if (onLike != null || onDislike != null) _buildActionButtons(context),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        if (onDislike != null)
          _buildActionButton(
            context,
            onTap: onDislike!,
            iconPath: Assets.images.svgs.icons.icClose.path,
          ),
        if (onDislike != null && onLike != null) buildSizedboxW(5.w),
        if (onLike != null)
          _buildActionButton(
            context,
            onTap: onLike!,
            iconPath: Assets.images.svgs.icons.icWirite.path,
          ),
      ],
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required VoidCallback onTap,
    required String iconPath,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 30.h,
        width: 30.w,
        decoration: BoxDecoration(
          color: Theme.of(context).customColors.fillColor,
          borderRadius: BorderRadius.circular(100.r),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.shade300,
              blurRadius: 5.r,
              offset: const Offset(1, 2),
            ),
          ],
        ),
        child: CustomImageView(
          imagePath: iconPath,
          margin: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
        ),
      ),
    );
  }

  Widget _buildUserDescription(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Text(
            user.about ?? 'N/A',
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: Theme.of(context).textTheme.bodyMedium!.copyWith(
              fontSize: 14.sp,
              color: Theme.of(context).customColors.darkGreytextcolor,
            ),
          ),
        ),
      ],
    );
  }
}
