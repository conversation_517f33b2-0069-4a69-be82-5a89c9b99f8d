import 'package:room_eight/core/api_config/endpoints/api_endpoint.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/viewmodels/like_bloc/like_bloc.dart';
import 'package:room_eight/views/like_view/widgets/like_screen_shimmer.dart';
import 'package:room_eight/widgets/common_widget/app_alert_dialog.dart';
import 'package:room_eight/views/chat/model/search_user_model.dart';

class LikeScreen extends StatefulWidget {
  const LikeScreen({super.key});

  static Widget builder(BuildContext context) => const LikeScreen();

  @override
  State<LikeScreen> createState() => _LikeScreenState();
}

class _LikeScreenState extends State<LikeScreen> {
  static const int _moveOutTabIndex = 0;
  static const int _moveInTabIndex = 1;

  @override
  void initState() {
    super.initState();
    _loadInitialData();
  }

  void _loadInitialData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final bloc = context.read<LikeBloc>();
      bloc.add(LoadMoveOutData());
      bloc.add(LoadMoveInData());
    });
  }

  void _navigateToChat(dynamic user) {
    final userId = _extractUserId(user);
    final chatUser = _createChatUser(user, userId);

    NavigatorService.pushNamed(
      AppRoutes.chatscreen,
      arguments: [chatUser, () {}],
    );
  }

  int? _extractUserId(dynamic user) {
    final userType = user.runtimeType.toString();

    if (userType.contains('MoveOutUser')) {
      return user.messageId ?? user.id;
    } else if (userType.contains('MoveInUser')) {
      return user.toMessageId ?? user.id;
    }
    return user.id;
  }

  SearchUserData _createChatUser(dynamic user, int? userId) {
    return SearchUserData(
      userId: userId,
      name: user.name,
      userName: user.name,
      profileImage: user.profileImage,
    );
  }

  void _onTabChanged(int index) {
    final bloc = context.read<LikeBloc>();
    bloc.add(TabChangedEvent(index));
    _loadDataForTab(index, bloc);
  }

  void _loadDataForTab(int index, LikeBloc bloc) {
    final state = bloc.state;

    if (index == _moveOutTabIndex &&
        state.moveOutData.isEmpty &&
        !state.isLoadingMoveOut) {
      bloc.add(LoadMoveOutData());
    } else if (index == _moveInTabIndex &&
        state.moveInData.isEmpty &&
        !state.isLoadingMoveIn) {
      bloc.add(LoadMoveInData());
    }
  }

  void _handleAcceptLike(dynamic user) {
    _showAcceptLikeDialog(user);
  }

  void _handleRejectLike(dynamic user) {
    if (user.id != null) {
      context.read<LikeBloc>().add(
        AcceptLikeEvent(profileId: user.id!, isAccept: false),
      );
    }
  }

  void _showAcceptLikeDialog(dynamic user) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return CustomAlertDialog(
          title: 'Accept Like',
          subtitle:
              'Are you sure you want to accept this like from ${user.name ?? 'this user'}?',
          confirmButtonText: 'Accept',
          cancelButtonText: 'Cancel',
          isLoading: false,
          onConfirmButtonPressed: () {
            Navigator.pop(dialogContext);
            if (user.id != null) {
              context.read<LikeBloc>().add(
                AcceptLikeEvent(
                  profileId: user.id!,
                  isAccept: true,
                  user: user,
                ),
              );
            }
          },
          onCancelButtonPressed: () {
            Navigator.pop(dialogContext);
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        backgroundColor: Theme.of(context).customColors.scaffoldColor,
        body: BlocConsumer<LikeBloc, LikeState>(
          listener: (context, state) {
            // Handle state changes if needed
          },
          builder: (context, state) {
            if (state.isLoadData || state.isAcceptingLike) {
              return const LikeScreenShimmer();
            }

            return _buildMainContent();
          },
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.r),
      child: Column(
        children: [
          buildSizedBoxH(50.h),
          _buildTabView(),
          Expanded(child: _buildPageView()),
        ],
      ),
    );
  }

  Widget _buildTabView() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 5.h),
      decoration: BoxDecoration(
        color: Theme.of(context).customColors.fillColor,
        borderRadius: BorderRadius.circular(100.r),
      ),
      child: TabBar(
        onTap: _onTabChanged,
        indicatorSize: TabBarIndicatorSize.tab,
        labelStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
          fontSize: 16.sp,
          fontWeight: FontWeight.w500,
        ),
        labelColor: Theme.of(context).customColors.fillColor,
        dividerColor: Colors.transparent,
        overlayColor: WidgetStateProperty.all(Colors.transparent),
        labelPadding: EdgeInsets.zero,
        indicator: ShapeDecoration(
          color: Theme.of(context).customColors.primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(100.r),
          ),
        ),
        tabs: [
          Tab(text: Lang.of(context).lbl_move_out),
          Tab(text: Lang.of(context).lbl_move_in),
        ],
      ),
    );
  }

  Widget _buildPageView() {
    return PageView(
      controller: context.read<LikeBloc>().pageController,
      physics: const NeverScrollableScrollPhysics(),
      children: [
        _buildLikeTabView(
          isLoadingFunc: (state) => state.isLoadingMoveOut,
          dataFunc: (state) => state.moveOutData,
          onRefresh: () => context.read<LikeBloc>().add(LoadMoveOutData()),
          emptyMessage: 'No Move Out data available',
          emptyIcon: Icons.favorite_border,
          onUserTap: _navigateToChat,
          onLike: null,
          onDislike: null,
        ),
        _buildLikeTabView(
          isLoadingFunc: (state) => state.isLoadingMoveIn,
          dataFunc: (state) => state.moveInData,
          onRefresh: () => context.read<LikeBloc>().add(LoadMoveInData()),
          emptyMessage: 'No Move In data available',
          emptyIcon: Icons.favorite,
          onUserTap: _navigateToChat,
          onLike: _handleAcceptLike,
          onDislike: _handleRejectLike,
        ),
      ],
    );
  }

  Widget _buildLikeTabView({
    required bool Function(LikeState) isLoadingFunc,
    required List<dynamic> Function(LikeState) dataFunc,
    required VoidCallback onRefresh,
    required String emptyMessage,
    required IconData emptyIcon,
    required Function(dynamic) onUserTap,
    required Function(dynamic)? onLike,
    required Function(dynamic)? onDislike,
  }) {
    return BlocBuilder<LikeBloc, LikeState>(
      builder: (context, state) {
        if (isLoadingFunc(state)) {
          return const LikeScreenShimmer();
        }

        final users = dataFunc(state);
        if (users.isEmpty) {
          return _buildEmptyState(
            context: context,
            emptyMessage: emptyMessage,
            emptyIcon: emptyIcon,
            onRefresh: onRefresh,
          );
        }

        return _buildUserList(
          context: context,
          users: users,
          onRefresh: onRefresh,
          onUserTap: onUserTap,
          onLike: onLike,
          onDislike: onDislike,
        );
      },
    );
  }

  Widget _buildEmptyState({
    required BuildContext context,
    required String emptyMessage,
    required IconData emptyIcon,
    required VoidCallback onRefresh,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(emptyIcon, size: 64.r, color: Colors.grey),
          buildSizedBoxH(16.h),
          Text(
            emptyMessage,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.grey,
              fontSize: 16.sp,
            ),
          ),
          buildSizedBoxH(20.h),
          ElevatedButton(onPressed: onRefresh, child: const Text('Refresh')),
        ],
      ),
    );
  }

  Widget _buildUserList({
    required BuildContext context,
    required List<dynamic> users,
    required VoidCallback onRefresh,
    required Function(dynamic) onUserTap,
    required Function(dynamic)? onLike,
    required Function(dynamic)? onDislike,
  }) {
    return RefreshIndicator(
      onRefresh: () async => onRefresh(),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Padding(
          padding: EdgeInsets.only(bottom: 80.w),
          child: Column(
            children: [
              buildSizedBoxH(20.h),
              ...users.map(
                (user) => _buildUserItem(
                  context: context,
                  user: user,
                  onUserTap: onUserTap,
                  onLike: onLike,
                  onDislike: onDislike,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUserItem({
    required BuildContext context,
    required dynamic user,
    required Function(dynamic) onUserTap,
    required Function(dynamic)? onLike,
    required Function(dynamic)? onDislike,
  }) {
    // Check user type and status
    final bool hasStatusTrue = user.status == true;
    final bool isMoveOutUser = user.runtimeType.toString().contains(
      'MoveOutUser',
    );
    final bool isMoveInUser = user.runtimeType.toString().contains(
      'MoveInUser',
    );

    // Determine card tap behavior
    VoidCallback? cardTapAction;

    if (isMoveOutUser) {
      // MoveOutUser: always allow navigation when status is true
      cardTapAction = hasStatusTrue ? () => onUserTap(user) : null;
    } else if (isMoveInUser) {
      // MoveInUser: never allow card tap navigation (only chat icon when status is true)
      cardTapAction = null;
    } else {
      // Default behavior for other user types
      cardTapAction = () => onUserTap(user);
    }

    // For MoveOutUser with status true, only allow navigation to chat
    final VoidCallback? likeAction = (hasStatusTrue && isMoveOutUser)
        ? null
        : (onLike != null ? () => onLike(user) : null);
    final VoidCallback? dislikeAction = (hasStatusTrue && isMoveOutUser)
        ? null
        : (onDislike != null ? () => onDislike(user) : null);

    return Column(
      children: [
        _buildUserProfileCard(
          context: context,
          user: user,
          onTap: cardTapAction ?? () {}, // Provide empty callback if null
          onUserTap: onUserTap, // Pass the original onUserTap for chat icon
          onLike: likeAction,
          onDislike: dislikeAction,
          onFavorite: () {}, // Placeholder for favorite functionality
        ),
        buildSizedBoxH(20.h),
      ],
    );
  }

  Widget _buildUserProfileCard({
    required BuildContext context,
    required dynamic user,
    required VoidCallback onTap,
    required Function(dynamic) onUserTap,
    required VoidCallback? onLike,
    required VoidCallback? onDislike,
    required VoidCallback onFavorite,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(8.r),
        decoration: _buildCardDecoration(context),
        child: Column(
          children: [
            _buildUserImage(context, user),
            buildSizedBoxH(12.h),
            _buildUserInfo(context, user, onUserTap, onLike, onDislike),
            _buildUserDescription(context, user),
          ],
        ),
      ),
    );
  }

  BoxDecoration _buildCardDecoration(BuildContext context) {
    return BoxDecoration(
      color: Theme.of(context).customColors.fillColor,
      borderRadius: BorderRadius.circular(16.r),
      boxShadow: const [
        BoxShadow(color: Colors.black12, blurRadius: 8, offset: Offset(0, 4)),
      ],
    );
  }

  Widget _buildUserImage(BuildContext context, dynamic user) {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(12.r),
          child: CustomImageView(
            imagePath: ApiEndPoint.getImageUrl + (user.profileImage ?? ''),
            height: 180.h,
            width: double.infinity,
            fit: BoxFit.cover,
          ),
        ),
      ],
    );
  }

  Widget _buildUserInfo(
    BuildContext context,
    dynamic user,
    Function(dynamic) onUserTap,
    VoidCallback? onLike,
    VoidCallback? onDislike,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          user.name?.isNotEmpty == true ? user.name! : 'Unknown User',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16.sp),
        ),
        _buildRightSection(context, user, onUserTap, onLike, onDislike),
      ],
    );
  }

  Widget _buildRightSection(
    BuildContext context,
    dynamic user,
    Function(dynamic) onUserTap,
    VoidCallback? onLike,
    VoidCallback? onDislike,
  ) {
    // Check if user has status field and it's true
    final bool hasStatusTrue = user.status == true;

    // For MoveInUser with status true, show chat icon instead of action buttons
    if (hasStatusTrue && _isMoveInUser(user)) {
      return _buildChatIcon(context, () => onUserTap(user));
    }

    // For MoveOutUser with status true, no action buttons (only navigation on tap)
    if (hasStatusTrue && _isMoveOutUser(user)) {
      return const SizedBox.shrink();
    }

    // Default behavior: show action buttons if available
    if (onLike != null || onDislike != null) {
      return _buildActionButtons(context, onLike, onDislike);
    }

    return const SizedBox.shrink();
  }

  bool _isMoveInUser(dynamic user) {
    return user.runtimeType.toString().contains('MoveInUser');
  }

  bool _isMoveOutUser(dynamic user) {
    return user.runtimeType.toString().contains('MoveOutUser');
  }

  Widget _buildChatIcon(BuildContext context, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 30.h,
        width: 30.w,
        decoration: BoxDecoration(
          color: Theme.of(context).customColors.primaryColor,
          borderRadius: BorderRadius.circular(100.r),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.shade300,
              blurRadius: 5.r,
              offset: const Offset(1, 2),
            ),
          ],
        ),
        child: Icon(Icons.chat, color: Colors.white, size: 16.sp),
      ),
    );
  }

  Widget _buildActionButtons(
    BuildContext context,
    VoidCallback? onLike,
    VoidCallback? onDislike,
  ) {
    return Row(
      children: [
        if (onDislike != null)
          _buildActionButton(
            context,
            onTap: onDislike,
            iconPath: Assets.images.svgs.icons.icClose.path,
          ),
        if (onDislike != null && onLike != null) buildSizedboxW(5.w),
        if (onLike != null)
          _buildActionButton(
            context,
            onTap: onLike,
            iconPath: Assets.images.svgs.icons.icWirite.path,
          ),
      ],
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required VoidCallback onTap,
    required String iconPath,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 30.h,
        width: 30.w,
        decoration: BoxDecoration(
          color: Theme.of(context).customColors.fillColor,
          borderRadius: BorderRadius.circular(100.r),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.shade300,
              blurRadius: 5.r,
              offset: const Offset(1, 2),
            ),
          ],
        ),
        child: CustomImageView(
          imagePath: iconPath,
          margin: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
        ),
      ),
    );
  }

  Widget _buildUserDescription(BuildContext context, dynamic user) {
    return Row(
      children: [
        Expanded(
          child: Text(
            user.about ?? 'N/A',
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: Theme.of(context).textTheme.bodyMedium!.copyWith(
              fontSize: 14.sp,
              color: Theme.of(context).customColors.darkGreytextcolor,
            ),
          ),
        ),
      ],
    );
  }
}
