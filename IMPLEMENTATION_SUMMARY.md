# Like Screen Status-Based UI Implementation

## Overview
This implementation adds conditional UI behavior to the Like Screen based on the `status` field in the API responses for `profile-like-inwards` and `profile-like-outwards` endpoints.

## API Response Structure

### profile-like-outwards (MoveOutUser)
```json
{
  "status": true,
  "message": "Data Found Successfully",
  "data": [
    {
      "id": 49,
      "name": "<EMAIL>",
      "year": "5",
      "profile_image": "/media/profile_pictures/...",
      "about": "My Name is <PERSON>",
      "message_id": 1,
      "status": false  // This field controls UI behavior
    }
  ]
}
```

### profile-like-inwards (MoveInUser)
```json
{
  "status": true,
  "message": "Data Found Successfully", 
  "data": [
    {
      "id": 48,
      "name": "Dhruv Sarang",
      "to_message_id": 28,
      "profile_image": "/media/profile_pictures/...",
      "about": "fffvv",
      "year": "4",
      "status": true  // This field controls UI behavior
    }
  ]
}
```

## Implementation Details

### 1. MoveOutUser Behavior
- **When `status: true`**: Only `_navigateToChat` should be available (no action buttons)
- **When `status: false`**: Normal behavior with action buttons

### 2. MoveInUser Behavior
- **When `status: true`**: Show chat icon instead of action buttons, only chat icon navigates to chat
- **When `status: false`**: Normal behavior with action buttons (like/dislike), NO navigation to chat

## Code Changes

### Modified Files
- `lib/views/like_view/like_screen.dart`

### Key Methods Added/Modified

#### 1. `_buildRightSection()` in `_UserProfileCard`
```dart
Widget _buildRightSection(BuildContext context) {
  final bool hasStatusTrue = user.status == true;
  
  // For MoveInUser with status true, show chat icon instead of action buttons
  if (hasStatusTrue && _isMoveInUser()) {
    return _buildChatIcon(context);
  }
  
  // For MoveOutUser with status true, no action buttons (only navigation on tap)
  if (hasStatusTrue && _isMoveOutUser()) {
    return const SizedBox.shrink();
  }
  
  // Default behavior: show action buttons if available
  if (onLike != null || onDislike != null) {
    return _buildActionButtons(context);
  }
  
  return const SizedBox.shrink();
}
```

#### 2. `_buildChatIcon()` in `_UserProfileCard`
```dart
Widget _buildChatIcon(BuildContext context) {
  return GestureDetector(
    onTap: onTap,
    child: Container(
      height: 30.h,
      width: 30.w,
      decoration: BoxDecoration(
        color: Theme.of(context).customColors.primaryColor,
        borderRadius: BorderRadius.circular(100.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade300,
            blurRadius: 5.r,
            offset: const Offset(1, 2),
          ),
        ],
      ),
      child: Icon(Icons.chat, color: Colors.white, size: 16.sp),
    ),
  );
}
```

#### 3. Modified `_buildUserItem()` in `_LikeTabView`
```dart
Widget _buildUserItem(BuildContext context, dynamic user) {
  final bool hasStatusTrue = user.status == true;
  final bool isMoveOutUser = user.runtimeType.toString().contains('MoveOutUser');
  
  // For MoveOutUser with status true, only allow navigation to chat
  final VoidCallback? likeAction = (hasStatusTrue && isMoveOutUser) 
      ? null 
      : (onLike != null ? () => onLike!(user) : null);
  final VoidCallback? dislikeAction = (hasStatusTrue && isMoveOutUser) 
      ? null 
      : (onDislike != null ? () => onDislike!(user) : null);
  
  return Column(
    children: [
      _UserProfileCard(
        user: user,
        onTap: () => onUserTap(user),
        onLike: likeAction,
        onDislike: dislikeAction,
        onFavorite: () {},
      ),
      buildSizedBoxH(20.h),
    ],
  );
}
```

## User Experience

### MoveOutUser with status: true
- User card shows profile information
- No action buttons (like/dislike) are displayed
- Tapping anywhere on the card navigates to chat screen
- Clean, minimal interface indicating established connection

### MoveInUser with status: true
- User card shows profile information
- Chat icon replaces action buttons
- **ONLY** tapping chat icon navigates to chat screen (card tap disabled)
- Visual indicator that conversation is available

### MoveInUser with status: false
- User card shows profile information
- Normal action buttons (like/dislike) are displayed
- **NO navigation to chat** (neither card tap nor chat icon)
- Standard interaction flow for pending likes

### MoveOutUser with status: false
- Normal behavior with action buttons
- Users can like/dislike as usual
- Standard interaction flow maintained

## Testing
A comprehensive test suite has been created in `test/like_screen_status_test.dart` to verify:
- MoveOutUser with status true shows no action buttons
- MoveInUser with status true shows chat icon
- Both user types with status false show normal action buttons
- Proper navigation behavior for all scenarios

## Benefits
1. **Clear Visual Feedback**: Users immediately understand interaction state
2. **Streamlined UX**: Reduces cognitive load by hiding irrelevant actions
3. **Consistent Behavior**: Status field uniformly controls UI across user types
4. **Maintainable Code**: Clean separation of concerns with helper methods
