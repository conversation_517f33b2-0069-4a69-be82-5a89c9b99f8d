import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/widgets/common_widget/common_shimmer.dart';

class LikeScreenShimmer extends StatelessWidget {
  const LikeScreenShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          buildSizedBoxH(20.h),
          ...List.generate(
            5, // Number of shimmer cards to show
            (index) => Padding(
              padding: EdgeInsets.only(bottom: 20.h),
              child: Container(
                padding: EdgeInsets.all(8.r),
                decoration: BoxDecoration(
                  color: Theme.of(context).customColors.fillColor,
                  borderRadius: BorderRadius.circular(16.r),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black12,
                      blurRadius: 8,
                      offset: Offset(0, 4),
                    ),
                  ],
                ),
                child: CommonShimmer(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Image shimmer with favorite button
                      Stack(
                        children: [
                          ShimmerBox(
                            height: 180.h,
                            width: double.infinity,
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                          Positioned(
                            top: 10.h,
                            right: 10.w,
                            child: ShimmerCircle(size: 40.r),
                          ),
                        ],
                      ),
                      buildSizedBoxH(12.h),
                      // Name and action buttons row
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // Name shimmer
                          ShimmerLine(
                            height: 16.h,
                            width: 120.w,
                            borderRadius: BorderRadius.circular(4.r),
                          ),
                          // Action buttons shimmer
                          Row(
                            children: [
                              ShimmerCircle(size: 30.r),
                              buildSizedboxW(5.w),
                              ShimmerCircle(size: 30.r),
                            ],
                          ),
                        ],
                      ),
                      buildSizedBoxH(8.h),
                      // About text shimmer (2 lines)
                      ShimmerLine(
                        height: 14.h,
                        width: double.infinity,
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      buildSizedBoxH(4.h),
                      ShimmerLine(
                        height: 14.h,
                        width: 200.w,
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          buildSizedBoxH(100.h),
        ],
      ),
    );
  }
}
