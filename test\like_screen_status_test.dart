import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:room_eight/models/move_in_model/move_in_model.dart';
import 'package:room_eight/models/move_out_model/move_out_model.dart';
import 'package:room_eight/viewmodels/like_bloc/like_bloc.dart';
import 'package:room_eight/views/like_view/like_screen.dart';

// Mock classes for testing
class MockLikeBloc extends Bloc<LikeEvent, LikeState> {
  MockLikeBloc() : super(LikeState());
  
  final PageController pageController = PageController();
}

void main() {
  group('LikeScreen Status Logic Tests', () {
    late MockLikeBloc mockLikeBloc;

    setUp(() {
      mockLikeBloc = MockLikeBloc();
    });

    tearDown(() {
      mockLikeBloc.close();
    });

    testWidgets('MoveOutUser with status true should only show navigation', (WidgetTester tester) async {
      // Create a MoveOutUser with status true
      final moveOutUser = MoveOutUser(
        id: 1,
        name: 'Test User',
        status: true, // Status is true
        profileImage: '/test/image.jpg',
        about: 'Test about',
        year: '4',
        messageId: 123,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<LikeBloc>.value(
            value: mockLikeBloc,
            child: Scaffold(
              body: _TestUserProfileCard(
                user: moveOutUser,
                onTap: () {},
                onLike: () {}, // This should be ignored when status is true
                onDislike: () {}, // This should be ignored when status is true
                onFavorite: () {},
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify that action buttons are not shown for MoveOutUser with status true
      expect(find.byIcon(Icons.close), findsNothing);
      expect(find.byIcon(Icons.check), findsNothing);
    });

    testWidgets('MoveInUser with status true should show chat icon', (WidgetTester tester) async {
      // Create a MoveInUser with status true
      final moveInUser = MoveInUser(
        id: 2,
        name: 'Test User 2',
        status: true, // Status is true
        profileImage: '/test/image2.jpg',
        about: 'Test about 2',
        year: '3',
        toMessageId: 456,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<LikeBloc>.value(
            value: mockLikeBloc,
            child: Scaffold(
              body: _TestUserProfileCard(
                user: moveInUser,
                onTap: () {},
                onLike: () {},
                onDislike: () {},
                onFavorite: () {},
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify that chat icon is shown for MoveInUser with status true
      expect(find.byIcon(Icons.chat), findsOneWidget);
    });

    testWidgets('MoveInUser with status false should show action buttons', (WidgetTester tester) async {
      // Create a MoveInUser with status false
      final moveInUser = MoveInUser(
        id: 3,
        name: 'Test User 3',
        status: false, // Status is false
        profileImage: '/test/image3.jpg',
        about: 'Test about 3',
        year: '2',
        toMessageId: 789,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<LikeBloc>.value(
            value: mockLikeBloc,
            child: Scaffold(
              body: _TestUserProfileCard(
                user: moveInUser,
                onTap: () {},
                onLike: () {},
                onDislike: () {},
                onFavorite: () {},
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify that action buttons are shown for MoveInUser with status false
      // Note: The actual icons depend on the implementation in Assets
      expect(find.byIcon(Icons.chat), findsNothing);
    });

    testWidgets('MoveOutUser with status false should show action buttons', (WidgetTester tester) async {
      // Create a MoveOutUser with status false
      final moveOutUser = MoveOutUser(
        id: 4,
        name: 'Test User 4',
        status: false, // Status is false
        profileImage: '/test/image4.jpg',
        about: 'Test about 4',
        year: '1',
        messageId: 101,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<LikeBloc>.value(
            value: mockLikeBloc,
            child: Scaffold(
              body: _TestUserProfileCard(
                user: moveOutUser,
                onTap: () {},
                onLike: () {},
                onDislike: () {},
                onFavorite: () {},
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // For MoveOutUser with status false, action buttons should be available
      // The exact implementation depends on the UI design
    });
  });
}

// Test wrapper for _UserProfileCard
class _TestUserProfileCard extends StatelessWidget {
  final dynamic user;
  final VoidCallback onTap;
  final VoidCallback? onLike;
  final VoidCallback? onDislike;
  final VoidCallback onFavorite;

  const _TestUserProfileCard({
    required this.user,
    required this.onTap,
    this.onLike,
    this.onDislike,
    required this.onFavorite,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(user.name ?? 'Unknown User'),
              _buildRightSection(context),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRightSection(BuildContext context) {
    // Check if user has status field and it's true
    final bool hasStatusTrue = user.status == true;
    
    // For MoveInUser with status true, show chat icon instead of action buttons
    if (hasStatusTrue && _isMoveInUser()) {
      return _buildChatIcon(context);
    }
    
    // For MoveOutUser with status true, no action buttons (only navigation on tap)
    if (hasStatusTrue && _isMoveOutUser()) {
      return const SizedBox.shrink();
    }
    
    // Default behavior: show action buttons if available
    if (onLike != null || onDislike != null) {
      return _buildActionButtons(context);
    }
    
    return const SizedBox.shrink();
  }

  bool _isMoveInUser() {
    return user.runtimeType.toString().contains('MoveInUser');
  }

  bool _isMoveOutUser() {
    return user.runtimeType.toString().contains('MoveOutUser');
  }

  Widget _buildChatIcon(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 30,
        width: 30,
        decoration: BoxDecoration(
          color: Colors.blue,
          borderRadius: BorderRadius.circular(100),
        ),
        child: const Icon(Icons.chat, color: Colors.white, size: 16),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        if (onDislike != null)
          GestureDetector(
            onTap: onDislike!,
            child: const Icon(Icons.close),
          ),
        if (onDislike != null && onLike != null) const SizedBox(width: 5),
        if (onLike != null)
          GestureDetector(
            onTap: onLike!,
            child: const Icon(Icons.check),
          ),
      ],
    );
  }
}
